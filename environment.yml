name: CxGrad
channels:
  - pytorch
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=4.5=1_gnu
  - blas=1.0=mkl
  - ca-certificates=2021.7.5=h06a4308_1
  - certifi=2021.5.30=py38h06a4308_0
  - cudatoolkit=11.0.221=h6bb024c_0
  - freetype=2.10.4=h5ab3b9f_0
  - intel-openmp=2021.3.0=h06a4308_3350
  - jpeg=9b=h024ee3a_2
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.35.1=h7274673_9
  - libffi=3.3=he6710b0_2
  - libgcc-ng=9.3.0=h5101ec6_17
  - libgomp=9.3.0=h5101ec6_17
  - libpng=1.6.37=hbc83047_0
  - libstdcxx-ng=9.3.0=hd4cf53a_17
  - libtiff=4.2.0=h85742a9_0
  - libuv=1.40.0=h7b6447c_0
  - libwebp-base=1.2.0=h27cfd23_0
  - lz4-c=1.9.3=h295c915_1
  - mkl=2021.3.0=h06a4308_520
  - mkl-service=2.4.0=py38h7f8727e_0
  - mkl_fft=1.3.0=py38h42c9631_2
  - mkl_random=1.2.2=py38h51133e4_0
  - ncurses=6.2=he6710b0_1
  - ninja=1.10.2=hff7bd54_1
  - numpy=1.20.3=py38hf144106_0
  - numpy-base=1.20.3=py38h74d4b33_0
  - olefile=0.46=py_0
  - openjpeg=2.4.0=h3ad879b_0
  - openssl=1.1.1k=h27cfd23_0
  - pillow=8.3.1=py38h2c7a002_0
  - pip=21.0.1=py38h06a4308_0
  - python=3.8.8=hdb3f193_5
  - pytorch=1.7.1=py3.8_cuda11.0.221_cudnn8.0.5_0
  - readline=8.1=h27cfd23_0
  - setuptools=52.0.0=py38h06a4308_0
  - six=1.16.0=pyhd3eb1b0_0
  - sqlite=3.36.0=hc218d9a_0
  - tk=8.6.10=hbc83047_0
  - torchvision=0.8.2=py38_cu110
  - typing_extensions=********=pyh06a4308_0
  - wheel=0.37.0=pyhd3eb1b0_0
  - xz=5.2.5=h7b6447c_0
  - zlib=1.2.11=h7b6447c_3
  - zstd=1.4.9=haebb681_0
  - pip:
    - tqdm==4.62.2
